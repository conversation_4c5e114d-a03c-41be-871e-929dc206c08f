import { useAuthStore } from '../store/authStore';
import { authService } from '../services/supabase';
import { useEffect } from 'react';

export const useAuth = () => {
  const {
    user,
    session,
    loading,
    isAuthenticated,
    error,
    rememberMe,
    signIn,
    signUp,
    signOut,
    updateProfile,
    resetPassword,
    verifyOTP,
    resendOTP,
    setUser,
    setSession,
    setLoading,
    setError,
    setRememberMe,
    clearError,
    initialize,
  } = useAuthStore();

  // Initialiser les credentials "Se souvenir de moi" au démarrage
  useEffect(() => {
    const loadRememberedCredentials = async () => {
      try {
        const credentials = await authService.getRememberedCredentials();
        if (credentials.rememberMe && credentials.email) {
          setRememberMe(true);
        }
      } catch (error) {
        console.error('Erreur chargement credentials:', error);
      }
    };

    loadRememberedCredentials();
  }, [setRememberMe]);

  // Méthodes utilitaires
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^(\+221|221)?[0-9]{8,9}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
  };

  const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('Le mot de passe doit contenir au moins 8 caractères');
    }
    if (!/[A-Z]/.test(password)) {
      errors.push('Le mot de passe doit contenir au moins une majuscule');
    }
    if (!/[a-z]/.test(password)) {
      errors.push('Le mot de passe doit contenir au moins une minuscule');
    }
    if (!/[0-9]/.test(password)) {
      errors.push('Le mot de passe doit contenir au moins un chiffre');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  };

  const getPasswordStrength = (password: string): 'weak' | 'medium' | 'strong' => {
    let score = 0;

    if (password.length >= 8) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;

    if (score <= 2) return 'weak';
    if (score <= 4) return 'medium';
    return 'strong';
  };

  // Connexion sociale (préparation pour l'implémentation future)
  const signInWithGoogle = async () => {
    try {
      setLoading(true);
      clearError();

      // TODO: Implémenter la connexion Google
      const result = await authService.signInWithProvider('google');

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur de connexion Google';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signInWithFacebook = async () => {
    try {
      setLoading(true);
      clearError();

      // TODO: Implémenter la connexion Facebook
      const result = await authService.signInWithProvider('facebook');

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur de connexion Facebook';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signInWithApple = async () => {
    try {
      setLoading(true);
      clearError();

      // TODO: Implémenter la connexion Apple
      const result = await authService.signInWithProvider('apple');

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur de connexion Apple';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return {
    // État
    user,
    session,
    loading,
    isAuthenticated,
    error,
    rememberMe,

    // Actions principales
    signIn,
    signUp,
    signOut,
    updateProfile,
    resetPassword,
    verifyOTP,
    resendOTP,

    // Actions utilitaires
    setUser,
    setSession,
    setLoading,
    setError,
    setRememberMe,
    clearError,
    initialize,

    // Validation
    validateEmail,
    validatePhone,
    validatePassword,
    getPasswordStrength,

    // Connexion sociale
    signInWithGoogle,
    signInWithFacebook,
    signInWithApple,

    // Vérifications de rôle
    isClient: user?.role === 'client',
    isLivreur: user?.role === 'livreur',
    isMarchand: user?.role === 'marchand',
    isAdmin: user?.role === 'admin',
  };
};
