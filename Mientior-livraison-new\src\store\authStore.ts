import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { User } from '../types';
import { authService, supabase } from '../services/supabase';

interface AuthState {
  user: User | null;
  session: any;
  loading: boolean;
  isAuthenticated: boolean;
  error: string | null;
  rememberMe: boolean;

  // Actions
  signIn: (email: string, password: string, rememberMe?: boolean) => Promise<any>;
  signUp: (email: string, password: string, userData: Partial<User>) => Promise<any>;
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<User>) => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  verifyOTP: (email: string, token: string, type: 'signup' | 'recovery') => Promise<any>;
  resendOTP: (email: string, type: 'signup' | 'recovery') => Promise<void>;
  setUser: (user: User | null) => void;
  setSession: (session: any) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setRememberMe: (remember: boolean) => void;
  initialize: () => Promise<void>;
  clearError: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      session: null,
      loading: true,
      isAuthenticated: false,
      error: null,
      rememberMe: false,

      signIn: async (email: string, password: string, rememberMe = false) => {
        try {
          set({ loading: true, error: null });

          const result = await authService.signIn(email, password);
          const user = await authService.getCurrentUser();

          // Sauvegarder les préférences "Se souvenir de moi"
          if (rememberMe) {
            await AsyncStorage.setItem('rememberedEmail', email);
            await AsyncStorage.setItem('rememberMe', 'true');
          } else {
            await AsyncStorage.removeItem('rememberedEmail');
            await AsyncStorage.removeItem('rememberMe');
          }

          set({
            user,
            session: result.session,
            isAuthenticated: !!user,
            loading: false,
            error: null,
            rememberMe,
          });

          return result;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erreur de connexion';
          set({
            loading: false,
            error: errorMessage,
            isAuthenticated: false,
            user: null,
            session: null
          });
          throw error;
        }
      },

      signUp: async (email: string, password: string, userData: Partial<User>) => {
        try {
          set({ loading: true, error: null });

          const result = await authService.signUp(email, password, userData);

          set({
            loading: false,
            error: null,
          });

          return result;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erreur d\'inscription';
          set({
            loading: false,
            error: errorMessage
          });
          throw error;
        }
      },

      signOut: async () => {
        try {
          set({ loading: true, error: null });

          await authService.signOut();

          // Nettoyer les données "Se souvenir de moi" si nécessaire
          const { rememberMe } = get();
          if (!rememberMe) {
            await AsyncStorage.removeItem('rememberedEmail');
            await AsyncStorage.removeItem('rememberMe');
          }

          set({
            user: null,
            session: null,
            isAuthenticated: false,
            loading: false,
            error: null,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erreur de déconnexion';
          set({
            loading: false,
            error: errorMessage
          });
          throw error;
        }
      },

      updateProfile: async (updates: Partial<User>) => {
        try {
          set({ loading: true, error: null });
          const { user } = get();
          if (!user) throw new Error('Utilisateur non connecté');

          const updatedUser = await authService.updateProfile(user.id, updates);

          set({
            user: updatedUser,
            loading: false,
            error: null
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erreur de mise à jour';
          set({
            loading: false,
            error: errorMessage
          });
          throw error;
        }
      },

      resetPassword: async (email: string) => {
        try {
          set({ loading: true, error: null });

          await authService.resetPassword(email);

          set({
            loading: false,
            error: null,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erreur de réinitialisation';
          set({
            loading: false,
            error: errorMessage
          });
          throw error;
        }
      },

      verifyOTP: async (email: string, token: string, type: 'signup' | 'recovery') => {
        try {
          set({ loading: true, error: null });

          const result = await authService.verifyOTP(email, token, type);

          if (result.user) {
            const user = await authService.getCurrentUser();
            set({
              user,
              session: result.session,
              isAuthenticated: !!user,
              loading: false,
              error: null,
            });
          } else {
            set({
              loading: false,
              error: null,
            });
          }

          return result;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Code de vérification incorrect';
          set({
            loading: false,
            error: errorMessage
          });
          throw error;
        }
      },

      resendOTP: async (email: string, type: 'signup' | 'recovery') => {
        try {
          set({ loading: true, error: null });

          await authService.resendOTP(email, type);

          set({
            loading: false,
            error: null,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erreur de renvoi du code';
          set({
            loading: false,
            error: errorMessage
          });
          throw error;
        }
      },

      setUser: (user: User | null) => {
        set({
          user,
          isAuthenticated: !!user
        });
      },

      setSession: (session: any) => {
        set({ session });
      },

      setLoading: (loading: boolean) => {
        set({ loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      setRememberMe: (remember: boolean) => {
        set({ rememberMe: remember });
      },

      clearError: () => {
        set({ error: null });
      },

      initialize: async () => {
        try {
          set({ loading: true });

          // Vérifier la session existante
          const { data: { session } } = await supabase.auth.getSession();
          
          if (session) {
            const user = await authService.getCurrentUser();
            set({
              user,
              session,
              isAuthenticated: !!user,
            });
          }
          
          // Écouter les changements d'authentification
          supabase.auth.onAuthStateChange(async (event, session) => {
            if (event === 'SIGNED_IN' && session) {
              const user = await authService.getCurrentUser();
              set({
                user,
                session,
                isAuthenticated: !!user,
              });
            } else if (event === 'SIGNED_OUT') {
              set({
                user: null,
                session: null,
                isAuthenticated: false,
              });
            }
          });
          
        } catch (error) {
          console.error('Erreur lors de l\'initialisation:', error);
        } finally {
          set({ loading: false });
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
); 