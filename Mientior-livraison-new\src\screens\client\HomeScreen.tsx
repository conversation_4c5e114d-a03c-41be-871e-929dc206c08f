import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  FlatList,
  Image,
  RefreshControl,

} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { colors } from '../../constants/theme';
import { useAuth } from '../../hooks/useAuth';
import { useRestaurants } from '../../hooks/useRestaurants';
import { useLocation } from '../../hooks/useLocation';
import { merchant } from '../../types';

interface ServiceType {
  id: string;
  title: string;
  icon: string;
  color: string;
  description: string;
}

const serviceTypes: ServiceType[] = [
  {
    id: 'repas',
    title: 'Repas',
    icon: '🍽️',
    color: '#FF6B6B',
    description: 'Restaurants et fast-food',
  },
  {
    id: 'colis',
    title: 'Colis',
    icon: '📦',
    color: '#4ECDC4',
    description: 'Documents et objets',
  },
  {
    id: 'courses',
    title: 'Courses',
    icon: '🛒',
    color: '#45B7D1',
    description: 'Produits et marchandises',
  },
];

export const HomeScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const { location, requestLocation } = useLocation();
  const { restaurants, loading, error, refresh } = useRestaurants(location || undefined);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedService, setSelectedService] = useState<string>('repas');

  useEffect(() => {
    // Demander la localisation au chargement
    requestLocation();
  }, []);

  const filteredRestaurants = restaurants.filter(restaurant => {
    const matchesSearch = restaurant.nom.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         restaurant.description?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesService = selectedService === 'repas' ? 
                          restaurant.type_merchant === 'restaurant' : 
                          restaurant.type_merchant === selectedService;
    return matchesSearch && matchesService;
  });

  const handleRestaurantPress = (restaurant: merchant) => {
    // TODO: Implémenter la navigation vers l'écran de détail du restaurant
    console.log('Navigation vers restaurant:', restaurant.nom, restaurant.id);
    // navigation.navigate('Establishment', { id: restaurant.id });
  };

  const handleServiceSelect = (serviceId: string) => {
    setSelectedService(serviceId);
  };

  const renderServiceCard = ({ item }: { item: ServiceType }) => (
    <TouchableOpacity
      style={[
        styles.serviceCard,
        { backgroundColor: item.color },
        selectedService === item.id && styles.serviceCardSelected,
      ]}
      onPress={() => handleServiceSelect(item.id)}
    >
      <Text style={styles.serviceIcon}>{item.icon}</Text>
      <Text style={styles.serviceTitle}>{item.title}</Text>
      <Text style={styles.serviceDescription}>{item.description}</Text>
    </TouchableOpacity>
  );

  const renderRestaurantCard = ({ item }: { item: merchant }) => (
    <TouchableOpacity
      style={styles.restaurantCard}
      onPress={() => handleRestaurantPress(item)}
    >
      <View style={styles.restaurantImageContainer}>
        {item.image_url ? (
          <Image source={{ uri: item.image_url }} style={styles.restaurantImage} />
        ) : (
          <View style={[styles.restaurantImage, styles.restaurantImagePlaceholder]}>
            <Text style={styles.restaurantImagePlaceholderText}>🏪</Text>
          </View>
        )}
        {item.is_active && (
          <View style={styles.openBadge}>
            <Text style={styles.openBadgeText}>Ouvert</Text>
          </View>
        )}
      </View>
      
      <View style={styles.restaurantInfo}>
        <Text style={styles.restaurantName} numberOfLines={1}>
          {item.nom}
        </Text>
        <Text style={styles.restaurantDescription} numberOfLines={2}>
          {item.description || 'Découvrez notre sélection'}
        </Text>
        
        <View style={styles.restaurantMeta}>
          <View style={styles.ratingContainer}>
            <Text style={styles.ratingIcon}>⭐</Text>
            <Text style={styles.ratingText}>
              {item.note_moyenne ? item.note_moyenne.toFixed(1) : '4.5'}
            </Text>
          </View>
          
          <View style={styles.deliveryInfo}>
            <Text style={styles.deliveryTime}>
              {item.temps_preparation_moyen || 30}-{(item.temps_preparation_moyen || 30) + 15} min
            </Text>
            <Text style={styles.deliveryFee}>
              {item.frais_livraison_base ? `${item.frais_livraison_base} FCFA` : 'Gratuit'}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      {/* Salutation */}
      <View style={styles.greeting}>
        <Text style={styles.greetingText}>
          Bonjour {user?.full_name?.split(' ')[0] || 'Client'} 👋
        </Text>
        <TouchableOpacity onPress={() => navigation.navigate('Location' as never)}>
          <Text style={styles.locationText}>
            📍 {location ? 'Position actuelle' : 'Localisation non disponible'} (Toucher pour changer)
          </Text>
        </TouchableOpacity>
      </View>

      {/* Barre de recherche */}
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="Rechercher un restaurant, plat..."
          placeholderTextColor={colors.text.secondary}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        <TouchableOpacity style={styles.searchButton}>
          <Text style={styles.searchButtonText}>🔍</Text>
        </TouchableOpacity>
      </View>

      {/* Types de services */}
      <View style={styles.servicesSection}>
        <Text style={styles.sectionTitle}>Que souhaitez-vous commander ?</Text>
        <FlatList
          data={serviceTypes}
          renderItem={renderServiceCard}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.servicesList}
        />
      </View>

      {/* Promotions */}
      <View style={styles.promotionsSection}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.promotionCard}>
            <Text style={styles.promotionTitle}>🎉 Livraison gratuite</Text>
            <Text style={styles.promotionSubtitle}>Sur votre première commande</Text>
          </View>
          <View style={styles.promotionCard}>
            <Text style={styles.promotionTitle}>⚡ Livraison express</Text>
            <Text style={styles.promotionSubtitle}>En moins de 20 minutes</Text>
          </View>
        </ScrollView>
      </View>

      {/* Titre de la section restaurants */}
      <View style={styles.restaurantsHeader}>
        <Text style={styles.sectionTitle}>
          {selectedService === 'repas' ? 'Restaurants populaires' : 
           selectedService === 'colis' ? 'Services de livraison' : 
           'Boutiques et magasins'}
        </Text>
        <TouchableOpacity>
          <Text style={styles.seeAllText}>Voir tout</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>❌ {error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={refresh}>
          <Text style={styles.retryButtonText}>Réessayer</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={filteredRestaurants}
        renderItem={renderRestaurantCard}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={loading}
            onRefresh={refresh}
            colors={[colors.primary[500]]}
            tintColor={colors.primary[500]}
          />
        }
        ListEmptyComponent={
          loading ? (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Chargement des restaurants...</Text>
            </View>
          ) : (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>
                {searchQuery ? 
                  `Aucun résultat pour "${searchQuery}"` : 
                  'Aucun restaurant disponible dans votre zone'
                }
              </Text>
            </View>
          )
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  content: {
    paddingBottom: 100, // Pour éviter que le contenu soit caché par la tab bar
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  greeting: {
    marginBottom: 20,
  },
  greetingText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginBottom: 4,
  },
  locationText: {
    fontSize: 14,
    color: colors.text.secondary,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: colors.text.primary,
    paddingVertical: 16,
  },
  searchButton: {
    padding: 8,
  },
  searchButtonText: {
    fontSize: 20,
  },
  servicesSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 16,
  },
  servicesList: {
    paddingRight: 20,
  },
  serviceCard: {
    width: 120,
    height: 100,
    borderRadius: 12,
    padding: 12,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  serviceCardSelected: {
    borderWidth: 2,
    borderColor: colors.primary[500],
  },
  serviceIcon: {
    fontSize: 24,
    marginBottom: 4,
  },
  serviceTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text.inverse,
    marginBottom: 2,
  },
  serviceDescription: {
    fontSize: 10,
    color: colors.text.inverse,
    textAlign: 'center',
    opacity: 0.9,
  },
  promotionsSection: {
    marginBottom: 24,
  },
  promotionCard: {
    backgroundColor: colors.primary[500],
    borderRadius: 12,
    padding: 16,
    marginRight: 12,
    minWidth: 200,
  },
  promotionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.inverse,
    marginBottom: 4,
  },
  promotionSubtitle: {
    fontSize: 14,
    color: colors.text.inverse,
    opacity: 0.9,
  },
  restaurantsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  seeAllText: {
    fontSize: 14,
    color: colors.primary[500],
    fontWeight: '500',
  },
  restaurantCard: {
    backgroundColor: colors.surface.primary,
    borderRadius: 12,
    marginHorizontal: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  restaurantImageContainer: {
    position: 'relative',
  },
  restaurantImage: {
    width: '100%',
    height: 160,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  restaurantImagePlaceholder: {
    backgroundColor: colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  restaurantImagePlaceholderText: {
    fontSize: 40,
  },
  openBadge: {
    position: 'absolute',
    top: 12,
    left: 12,
    backgroundColor: colors.success,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  openBadgeText: {
    fontSize: 12,
    color: colors.text.inverse,
    fontWeight: '500',
  },
  restaurantInfo: {
    padding: 16,
  },
  restaurantName: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 4,
  },
  restaurantDescription: {
    fontSize: 14,
    color: colors.text.secondary,
    marginBottom: 12,
    lineHeight: 20,
  },
  restaurantMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingIcon: {
    fontSize: 16,
    marginRight: 4,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text.primary,
  },
  deliveryInfo: {
    alignItems: 'flex-end',
  },
  deliveryTime: {
    fontSize: 14,
    color: colors.text.primary,
    fontWeight: '500',
  },
  deliveryFee: {
    fontSize: 12,
    color: colors.text.secondary,
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: colors.text.secondary,
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: colors.text.secondary,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: colors.error,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: colors.primary[500],
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: colors.text.inverse,
    fontSize: 16,
    fontWeight: '500',
  },
});
