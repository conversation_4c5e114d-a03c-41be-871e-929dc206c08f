import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  StatusBar,
  Animated,
  Alert,
  Platform,
  Dimensions,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../hooks/useAuth';

const { width } = Dimensions.get('window');

interface RouteParams {
  phone?: string;
  email: string;
  type: 'signup' | 'recovery';
}

const OTPVerificationScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { phone, email, type } = route.params as RouteParams;
  const { verifyOTP, resendOTP, loading, error, clearError } = useAuth();

  // État
  const [otp, setOtp] = useState<string[]>(['', '', '', '', '', '']);
  const [timer, setTimer] = useState(180); // 3 minutes
  const [canResend, setCanResend] = useState(false);
  const [isVoiceCall, setIsVoiceCall] = useState(false);

  // Références pour les inputs
  const inputRefs = useRef<(TextInput | null)[]>([]);

  // Animations
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    // Animation d'entrée
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Timer
    const interval = setInterval(() => {
      setTimer((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Auto-vérification quand le code est complet
    const otpCode = otp.join('');
    if (otpCode.length === 6) {
      handleVerifyOtp();
    }

    return () => clearInterval(interval);
  }, [otp]);

  // Gestion des changements OTP
  const handleOtpChange = (value: string, index: number) => {
    if (value.length > 1) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus sur le champ suivant
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  // Gestion des touches
  const handleKeyPress = (key: string, index: number) => {
    if (key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  // Formatage du timer
  const formatTimer = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Vérification OTP
  const handleVerifyOtp = async () => {
    const otpCode = otp.join('');
    if (otpCode.length !== 6) {
      Alert.alert('Code incomplet', 'Veuillez saisir les 6 chiffres du code.');
      return;
    }

    try {
      clearError();
      const result = await verifyOTP(email, otpCode, type);

      if (type === 'signup') {
        Alert.alert(
          'Inscription réussie',
          'Votre compte a été créé avec succès !',
          [
            {
              text: 'OK',
              onPress: () => navigation.navigate('RoleSelection' as never),
            },
          ]
        );
      } else {
        Alert.alert(
          'Vérification réussie',
          'Vous pouvez maintenant créer un nouveau mot de passe.',
          [
            {
              text: 'OK',
              onPress: () => navigation.navigate('ResetPassword' as never, { email }),
            },
          ]
        );
      }
    } catch (error) {
      console.error('Erreur vérification OTP:', error);
      setOtp(['', '', '', '', '', '']);
      inputRefs.current[0]?.focus();
    }
  };

  // Renvoyer le code
  const handleResendCode = async () => {
    try {
      clearError();
      await resendOTP(email, type);
      setTimer(180);
      setCanResend(false);
      setOtp(['', '', '', '', '', '']);
      setIsVoiceCall(false);

      Alert.alert(
        'Code renvoyé',
        `Un nouveau code a été envoyé ${isVoiceCall ? 'par appel vocal' : 'par SMS/Email'}.`
      );
    } catch (error) {
      console.error('Erreur renvoi OTP:', error);
    }
  };

  // Appel vocal (simulation)
  const handleVoiceCall = async () => {
    try {
      clearError();
      setIsVoiceCall(true);
      // TODO: Implémenter l'appel vocal réel
      await resendOTP(email, type);
      setTimer(180);
      setCanResend(false);

      Alert.alert(
        'Appel vocal programmé',
        'Vous allez recevoir un appel vocal dans quelques instants avec votre code de vérification.'
      );
    } catch (error) {
      console.error('Erreur appel vocal:', error);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />
      
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="#1F2937" />
          </TouchableOpacity>

          <Text style={styles.title}>Vérification</Text>
          <Text style={styles.subtitle}>
            {type === 'signup'
              ? 'Nous avons envoyé un code de vérification à votre email'
              : 'Nous avons envoyé un code de réinitialisation à votre email'
            }
          </Text>
          <Text style={styles.phoneNumber}>
            {email.replace(/(.{2})(.*)(@.*)/, '$1***$3')}
          </Text>

          {error && (
            <View style={styles.errorContainer}>
              <Ionicons name="alert-circle" size={20} color="#EF4444" />
              <Text style={styles.errorText}>{error}</Text>
            </View>
          )}
        </View>

        {/* Code OTP */}
        <View style={styles.otpContainer}>
          <View style={styles.otpInputs}>
            {otp.map((digit, index) => (
              <View key={index} style={styles.otpInputWrapper}>
                <TextInput
                  ref={(ref) => {
                    inputRefs.current[index] = ref;
                  }}
                  style={[
                    styles.otpInput,
                    digit && styles.otpInputFilled,
                  ]}
                  value={digit}
                  onChangeText={(value) => handleOtpChange(value, index)}
                  onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
                  keyboardType="numeric"
                  maxLength={1}
                  selectTextOnFocus
                  textAlign="center"
                />
                {digit && <View style={styles.dot} />}
              </View>
            ))}
          </View>
        </View>

        {/* Timer */}
        <View style={styles.timerContainer}>
          <Text style={styles.timerLabel}>Le code expire dans:</Text>
          <Text style={styles.timerText}>{formatTimer(timer)}</Text>
        </View>

        {/* Bouton de vérification manuelle */}
        <TouchableOpacity
          style={[
            styles.verifyButton,
            (otp.join('').length !== 6 || loading) && styles.disabledButton
          ]}
          onPress={handleVerifyOtp}
          disabled={otp.join('').length !== 6 || loading}
        >
          <Text style={[
            styles.verifyButtonText,
            (otp.join('').length !== 6 || loading) && styles.disabledButtonText
          ]}>
            {loading ? 'Vérification...' : 'Vérifier le code'}
          </Text>
        </TouchableOpacity>

        {/* Resend */}
        <View style={styles.resendContainer}>
          {canResend ? (
            <TouchableOpacity onPress={handleResendCode} disabled={loading}>
              <Text style={[styles.resendText, loading && styles.disabledText]}>
                Renvoyer le code
              </Text>
            </TouchableOpacity>
          ) : (
            <Text style={styles.resendDisabledText}>
              Renvoyer le code dans {formatTimer(timer)}
            </Text>
          )}
        </View>

        {/* Alternative method */}
        <View style={styles.alternativeContainer}>
          <Text style={styles.alternativeQuestion}>
            Vous n'avez pas reçu l'email?
          </Text>
          <TouchableOpacity onPress={handleVoiceCall} disabled={loading}>
            <Text style={[styles.alternativeMethod, loading && styles.disabledText]}>
              {isVoiceCall ? 'Appel vocal programmé' : 'Recevoir par appel vocal'}
            </Text>
          </TouchableOpacity>

          <View style={styles.infoContainer}>
            <Ionicons name="information-circle-outline" size={16} color="#6B7280" />
            <Text style={styles.infoText}>
              Un appel vocal vous dictera le code de vérification
            </Text>
          </View>
        </View>
      </Animated.View>
      
      {/* Bottom Indicator */}
      <View style={styles.bottomIndicator}>
        <View style={styles.indicator} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  header: {
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 40,
    position: 'relative',
  },
  backButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 60 : 40,
    left: 0,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 16,
    marginTop: 40,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 8,
    fontWeight: '400',
  },
  phoneNumber: {
    fontSize: 16,
    color: '#1F2937',
    fontWeight: '600',
    marginBottom: 40,
  },
  otpContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  otpInputs: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    maxWidth: 300,
    gap: 12,
  },
  otpInputWrapper: {
    position: 'relative',
    flex: 1,
  },
  otpInput: {
    height: 56,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    textAlign: 'center',
  },
  otpInputFilled: {
    borderColor: '#0DCAA8',
    backgroundColor: '#F0FDF4',
  },
  dot: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#1F2937',
    transform: [{ translateX: -4 }, { translateY: -4 }],
  },
  timerContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  timerLabel: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
    fontWeight: '400',
  },
  timerText: {
    fontSize: 18,
    color: '#0DCAA8',
    fontWeight: '600',
  },
  resendContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  resendText: {
    fontSize: 16,
    color: '#0DCAA8',
    fontWeight: '600',
  },
  alternativeContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  alternativeQuestion: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 12,
    fontWeight: '400',
  },
  alternativeMethod: {
    fontSize: 16,
    color: '#0DCAA8',
    fontWeight: '600',
    marginBottom: 20,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingHorizontal: 20,
    maxWidth: 280,
  },
  infoText: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 8,
    lineHeight: 20,
    fontWeight: '400',
    textAlign: 'center',
  },
  bottomIndicator: {
    alignItems: 'center',
    paddingBottom: Platform.OS === 'ios' ? 34 : 20,
    paddingTop: 8,
  },
  indicator: {
    width: 134,
    height: 5,
    backgroundColor: '#000000',
    borderRadius: 3,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF2F2',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  errorText: {
    fontSize: 14,
    color: '#EF4444',
    marginLeft: 8,
    flex: 1,
  },
  verifyButton: {
    backgroundColor: '#0DCAA8',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#0DCAA8',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  disabledButton: {
    backgroundColor: '#9CA3AF',
    shadowOpacity: 0.1,
  },
  verifyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  disabledButtonText: {
    color: '#FFFFFF',
    opacity: 0.7,
  },
  disabledText: {
    opacity: 0.5,
  },
  resendDisabledText: {
    fontSize: 16,
    color: '#9CA3AF',
    fontWeight: '400',
  },
});

export default OTPVerificationScreen;
